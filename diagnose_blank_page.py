#!/usr/bin/env python3
"""
页面空白问题诊断脚本
"""

import requests
import json
import time
from urllib.parse import urljoin

def test_backend_api():
    """测试后端API"""
    print("🔍 测试后端API...")
    
    base_url = "http://localhost:8000"
    endpoints = [
        "/api/v1/market/health",
        "/api/v1/market/overview", 
        "/docs",  # FastAPI文档
    ]
    
    results = []
    for endpoint in endpoints:
        try:
            url = urljoin(base_url, endpoint)
            response = requests.get(url, timeout=5)
            
            status = "✅ 正常" if response.status_code == 200 else f"❌ {response.status_code}"
            results.append(f"  {endpoint}: {status}")
            
            if endpoint == "/api/v1/market/overview" and response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"    📊 市场数据正常: {len(data.get('data', {}).get('indices', []))}个指数")
                
        except Exception as e:
            results.append(f"  {endpoint}: ❌ 错误 - {str(e)}")
    
    print("后端API测试结果:")
    for result in results:
        print(result)
    print()

def test_frontend_service():
    """测试前端服务"""
    print("🔍 测试前端服务...")
    
    frontend_urls = [
        "http://localhost:5174",
        "http://localhost:5173",
    ]
    
    for url in frontend_urls:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"  ✅ 前端服务正常: {url}")
                
                # 检查HTML内容
                html = response.text
                if '<div id="app">' in html or '<div id="root">' in html:
                    print("    📦 HTML结构正常，包含应用容器")
                else:
                    print("    ⚠️ HTML中未找到应用容器")
                
                if 'main.ts' in html or 'main.js' in html:
                    print("    📜 主要脚本文件已引用")
                else:
                    print("    ⚠️ 未找到主要脚本文件引用")
                
                return True
                
        except Exception as e:
            print(f"  ❌ {url}: {str(e)}")
    
    print("  ❌ 前端服务不可访问")
    return False

def check_cors_issues():
    """检查CORS问题"""
    print("🔍 检查CORS配置...")
    
    try:
        # 模拟浏览器的预检请求
        response = requests.options(
            "http://localhost:8000/api/v1/market/health",
            headers={
                'Origin': 'http://localhost:5174',
                'Access-Control-Request-Method': 'GET',
                'Access-Control-Request-Headers': 'Content-Type'
            },
            timeout=5
        )
        
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
        }
        
        print("CORS配置:")
        for header, value in cors_headers.items():
            if value:
                print(f"  ✅ {header}: {value}")
            else:
                print(f"  ❌ {header}: 未设置")
        
    except Exception as e:
        print(f"  ❌ CORS检查失败: {str(e)}")
    print()

def test_market_data():
    """测试市场数据"""
    print("🔍 测试市场数据...")
    
    try:
        response = requests.get("http://localhost:8000/api/v1/market/overview", timeout=5)
        if response.status_code == 200:
            data = response.json()
            if data.get('success'):
                market_data = data.get('data', {})
                print(f"  ✅ 市场状态: {market_data.get('market_status', '未知')}")
                print(f"  ✅ 数据时间: {market_data.get('timestamp', '未知')}")
                
                stats = market_data.get('stats', {})
                print(f"  📊 统计: 涨{stats.get('advancers', 0)}只, 跌{stats.get('decliners', 0)}只")
                
                indices = market_data.get('indices', [])
                print(f"  📈 指数数量: {len(indices)}")
                for idx in indices[:2]:  # 显示前两个指数
                    print(f"    - {idx.get('name')}: {idx.get('current')} ({idx.get('change_percent')}%)")
            else:
                print(f"  ❌ API返回错误: {data}")
        else:
            print(f"  ❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"  ❌ 市场数据测试失败: {str(e)}")
    print()

def provide_solutions():
    """提供解决方案"""
    print("🔧 可能的解决方案:")
    print()
    
    print("1. 前端问题诊断:")
    print("   - 检查浏览器开发者工具的Console标签是否有JavaScript错误")
    print("   - 检查Network标签是否有请求失败")
    print("   - 尝试硬刷新页面 (Ctrl+F5)")
    print("   - 清除浏览器缓存")
    print()
    
    print("2. 如果前端服务正常但页面空白:")
    print("   - 检查Vue应用是否正确挂载到#app元素")
    print("   - 检查路由配置是否正确")
    print("   - 检查是否有认证拦截导致页面无法加载")
    print()
    
    print("3. 如果API请求失败:")
    print("   - 检查CORS配置")
    print("   - 检查API基础URL配置")
    print("   - 检查网络连接")
    print()
    
    print("4. 快速修复命令:")
    print("   前端重启: cd frontend && pnpm dev")
    print("   后端重启: cd backend && python -m uvicorn app.main:app --reload")
    print("   清除缓存: 删除 frontend/node_modules/.vite 目录")
    print()

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 量化投资平台 - 页面空白问题诊断")
    print("=" * 60)
    print()
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    time.sleep(2)
    
    # 运行诊断
    test_backend_api()
    frontend_ok = test_frontend_service()
    check_cors_issues()
    test_market_data()
    
    # 提供解决方案
    provide_solutions()
    
    # 给出建议
    print("💡 建议操作:")
    if frontend_ok:
        print("   ✅ 前端服务正常，请检查浏览器开发者工具")
        print("   🌐 直接访问: http://localhost:5174")
        print("   📋 测试页面: http://localhost:5174/quick_test.html")
    else:
        print("   ❌ 前端服务异常，请重启前端服务")
        print("   💻 重启命令: cd frontend && pnpm dev")
    
    print()
    print("=" * 60)

if __name__ == "__main__":
    main()
#!/bin/bash

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================"
echo -e "    量化投资平台 - 停止服务脚本"
echo -e "========================================${NC}"
echo

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

echo -e "${YELLOW}🛑 正在停止所有相关服务...${NC}"

# 如果存在PID文件，优先使用
if [ -f "$SCRIPT_DIR/.pids" ]; then
    echo -e "${BLUE}从PID文件停止服务...${NC}"
    PIDS=$(cat "$SCRIPT_DIR/.pids")
    for pid in $PIDS; do
        if kill -0 $pid 2>/dev/null; then
            echo -e "${YELLOW}停止进程 $pid${NC}"
            kill $pid 2>/dev/null
            sleep 1
            # 如果进程仍然存在，强制杀死
            if kill -0 $pid 2>/dev/null; then
                kill -9 $pid 2>/dev/null
            fi
        fi
    done
    rm -f "$SCRIPT_DIR/.pids"
fi

# 停止所有相关进程
echo -e "${BLUE}停止后端服务...${NC}"
pkill -f "uvicorn.*app.main:app" 2>/dev/null
pkill -f "python.*app.main" 2>/dev/null

echo -e "${BLUE}停止前端服务...${NC}"
pkill -f "vite.*dev" 2>/dev/null
pkill -f "pnpm.*dev" 2>/dev/null

# 停止可能占用端口的进程
echo -e "${BLUE}检查端口占用...${NC}"
if command -v lsof &> /dev/null; then
    # 停止占用8000端口的进程（后端）
    BACKEND_PID=$(lsof -ti:8000 2>/dev/null)
    if [ ! -z "$BACKEND_PID" ]; then
        echo -e "${YELLOW}停止占用8000端口的进程: $BACKEND_PID${NC}"
        kill $BACKEND_PID 2>/dev/null
    fi
    
    # 停止占用5173端口的进程（前端）
    FRONTEND_PID=$(lsof -ti:5173 2>/dev/null)
    if [ ! -z "$FRONTEND_PID" ]; then
        echo -e "${YELLOW}停止占用5173端口的进程: $FRONTEND_PID${NC}"
        kill $FRONTEND_PID 2>/dev/null
    fi
elif command -v netstat &> /dev/null; then
    # 使用netstat查找进程（Linux）
    BACKEND_PID=$(netstat -tlnp 2>/dev/null | grep :8000 | awk '{print $7}' | cut -d'/' -f1)
    if [ ! -z "$BACKEND_PID" ]; then
        echo -e "${YELLOW}停止占用8000端口的进程: $BACKEND_PID${NC}"
        kill $BACKEND_PID 2>/dev/null
    fi
    
    FRONTEND_PID=$(netstat -tlnp 2>/dev/null | grep :5173 | awk '{print $7}' | cut -d'/' -f1)
    if [ ! -z "$FRONTEND_PID" ]; then
        echo -e "${YELLOW}停止占用5173端口的进程: $FRONTEND_PID${NC}"
        kill $FRONTEND_PID 2>/dev/null
    fi
fi

# 清理日志文件（可选）
read -p "是否清理日志文件? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}清理日志文件...${NC}"
    rm -f "$SCRIPT_DIR/backend/backend.log"
    rm -f "$SCRIPT_DIR/frontend/frontend.log"
    echo -e "${GREEN}✅ 日志文件已清理${NC}"
fi

echo
echo -e "${GREEN}✅ 服务停止完成！${NC}"
echo

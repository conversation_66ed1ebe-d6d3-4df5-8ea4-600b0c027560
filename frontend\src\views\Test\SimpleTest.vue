<template>
  <div class="simple-test">
    <h1>简单测试页面</h1>
    <p>当前时间: {{ currentTime }}</p>
    <el-button type="primary" @click="testClick">测试按钮</el-button>
    <div v-if="message" class="message">{{ message }}</div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'

const currentTime = ref('')
const message = ref('')

const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

const testClick = () => {
  message.value = '按钮点击成功！'
  ElMessage.success('测试成功')
}

onMounted(() => {
  updateTime()
  setInterval(updateTime, 1000)
})
</script>

<style scoped>
.simple-test {
  padding: 20px;
  text-align: center;
}

.message {
  margin-top: 20px;
  padding: 10px;
  background-color: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 4px;
  color: #0369a1;
}
</style>

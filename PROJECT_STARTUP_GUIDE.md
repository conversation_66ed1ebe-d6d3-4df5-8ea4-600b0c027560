# 量化投资平台 - 项目启动指南

## 🚀 快速启动

### Windows 用户
```bash
# 双击运行或在命令行执行
start-project.bat
```

### Linux/Mac 用户
```bash
# 给脚本添加执行权限（首次运行）
chmod +x start-project.sh stop-project.sh

# 启动项目
./start-project.sh
```

## 📋 环境要求

### 必需软件
- **Python 3.8+** - 后端运行环境
- **Node.js 16+** - 前端运行环境  
- **pnpm** - 前端包管理器

### 安装 pnpm
```bash
npm install -g pnpm
```

## 🔧 手动启动（如果脚本失败）

### 1. 启动后端
```bash
cd backend

# 创建虚拟环境（首次运行）
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate.bat
# Linux/Mac:
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt

# 启动后端服务
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 2. 启动前端
```bash
cd frontend

# 安装依赖（首次运行）
pnpm install

# 启动前端服务
pnpm run dev
```

## 🌐 访问地址

启动成功后，可以通过以下地址访问：

- **前端界面**: http://localhost:5173
- **后端API**: http://localhost:8000  
- **API文档**: http://localhost:8000/docs
- **WebSocket**: ws://localhost:8000/ws

## 🛑 停止服务

### Windows
```bash
stop-project.bat
```

### Linux/Mac
```bash
./stop-project.sh
```

## 📊 行情中心功能测试

启动成功后，可以测试以下功能：

### 1. 实时行情
- 访问: http://localhost:5173/market/realtime
- 功能: 实时股票价格、五档盘口、成交数据

### 2. 历史数据
- 访问: http://localhost:5173/market/historical  
- 功能: 历史K线数据、数据导出、统计分析

### 3. WebSocket实时推送
- 测试页面: http://localhost:5173/public/websocket-test.html
- 功能: 实时行情推送、连接状态监控

## 🔍 故障排除

### 常见问题

#### 1. 后端启动失败
```bash
# 检查Python版本
python --version

# 检查依赖安装
pip list | grep fastapi
pip list | grep uvicorn

# 重新安装依赖
pip install -r requirements.txt --force-reinstall
```

#### 2. 前端启动失败
```bash
# 检查Node.js版本
node --version

# 检查pnpm安装
pnpm --version

# 清理并重新安装
pnpm store prune
rm -rf node_modules
pnpm install
```

#### 3. 端口被占用
```bash
# Windows - 查找占用端口的进程
netstat -ano | findstr :8000
netstat -ano | findstr :5173

# Linux/Mac - 查找占用端口的进程  
lsof -i :8000
lsof -i :5173

# 杀死进程
# Windows: taskkill /PID <PID> /F
# Linux/Mac: kill -9 <PID>
```

#### 4. 数据库问题
```bash
# 删除数据库文件重新初始化
rm data/quantplatform.db

# 重启后端服务
```

## 📁 项目结构

```
quant-platform/
├── backend/                 # 后端服务
│   ├── app/                # 应用代码
│   ├── data/               # 数据存储
│   ├── requirements.txt    # Python依赖
│   └── venv/              # 虚拟环境
├── frontend/               # 前端应用
│   ├── src/               # 源代码
│   ├── public/            # 静态资源
│   ├── package.json       # 项目配置
│   └── node_modules/      # 依赖包
├── start-project.bat      # Windows启动脚本
├── start-project.sh       # Linux/Mac启动脚本
├── stop-project.bat       # Windows停止脚本
└── stop-project.sh        # Linux/Mac停止脚本
```

## 🎯 下一步

1. **测试行情中心功能** - 验证实时行情和历史数据
2. **检查API接口** - 访问 http://localhost:8000/docs
3. **查看日志** - 检查控制台输出和日志文件
4. **性能监控** - 观察内存和CPU使用情况

## 📞 技术支持

如果遇到问题，请检查：
1. 控制台错误信息
2. 后端日志: `backend/backend.log` (Linux/Mac)
3. 前端日志: `frontend/frontend.log` (Linux/Mac)
4. 浏览器开发者工具控制台

---

**注意**: 首次启动可能需要较长时间来安装依赖和初始化数据库。

/**
 * 行情API适配器
 * 统一前端API调用，实现增强版->基础版->Mock的降级机制
 */

import { httpClient } from '@/api/http'
import type { ApiResponse } from '@/types/api'

// API路径配置
const API_PATHS = {
  // 增强版API路径（优先）
  enhanced: {
    stock: (symbol: string) => `/market/enhanced-market/stock/${symbol}`,
    quotes: (symbols: string) => `/market/enhanced-market/quotes?symbols=${symbols}`,
    kline: (symbol: string, period = '1d', limit = 100) =>
      `/market/enhanced-market/kline/${symbol}?period=${period}&limit=${limit}`,
    search: (query: string, limit = 20) =>
      `/market/enhanced-market/search?query=${query}&limit=${limit}`,
    watchlist: () => `/market/enhanced-market/watchlist`,
    addWatchlist: (symbol: string) => `/market/enhanced-market/watchlist?symbol=${symbol}`,
    removeWatchlist: (symbol: string) => `/market/enhanced-market/watchlist/${symbol}`,
    health: () => `/market/enhanced-market/health`
  },

  // 基础版API路径（fallback）
  basic: {
    stock: (symbol: string) => `/api/v1/market/quotes/${symbol}`,
    quotes: (symbols: string) => `/api/v1/market/quotes/realtime?symbols=${symbols}`,
    kline: (symbol: string, period = '1d', limit = 100) =>
      `/api/v1/market/kline/${symbol}?period=${period}&limit=${limit}`,
    search: (query: string, limit = 20) =>
      `/api/v1/market/search?keyword=${query}&limit=${limit}`,
    stockList: (market?: string, limit = 100) =>
      `/market/stocks/list?${market ? `market=${market}&` : ''}limit=${limit}`,
    overview: () => `/market/overview`,
    sectors: () => `/market/sectors/performance`,
    watchlist: () => `/market/watchlist`,
    addWatchlist: (symbol: string) => `/market/watchlist/${symbol}`,
    removeWatchlist: (symbol: string) => `/market/watchlist/${symbol}`,
    health: () => `/market/health`
  }
}

// 通用API调用函数
async function callWithFallback<T>(
  enhancedPath: string,
  basicPath: string,
  method: 'GET' | 'POST' | 'DELETE' = 'GET',
  data?: any
): Promise<ApiResponse<T>> {
  try {
    // 1. 尝试增强版API
    const response = await httpClient.request({
      method,
      url: enhancedPath,
      data
    })

    console.log(`✅ 增强版API成功: ${enhancedPath}`)
    return response.data

  } catch (enhancedError) {
    console.warn(`⚠️ 增强版API失败: ${enhancedPath}`, enhancedError)

    try {
      // 2. 降级到基础版API
      const response = await httpClient.request({
        method,
        url: basicPath,
        data
      })

      console.log(`✅ 基础版API成功: ${basicPath}`)
      return response.data

    } catch (basicError) {
      console.error(`❌ 基础版API也失败: ${basicPath}`, basicError)

      // 3. 返回Mock数据或抛出错误
      throw new Error(`所有API都失败: ${enhancedError.message}`)
    }
  }
}

// 导出的API方法
export const marketApi = {
  // 获取股票详情
  async getStock(symbol: string) {
    return callWithFallback(
      API_PATHS.enhanced.stock(symbol),
      API_PATHS.basic.stock(symbol)
    )
  },

  // 获取批量行情
  async getQuotes(symbols: string[]) {
    const symbolsStr = symbols.join(',')
    return callWithFallback(
      API_PATHS.enhanced.quotes(symbolsStr),
      API_PATHS.basic.quotes(symbolsStr)
    )
  },

  // 获取K线数据
  async getKlineData(symbol: string, period = '1d', limit = 100) {
    return callWithFallback(
      API_PATHS.enhanced.kline(symbol, period, limit),
      API_PATHS.basic.kline(symbol, period, limit)
    )
  },

  // 搜索股票
  async searchStocks(query: string, limit = 20) {
    return callWithFallback(
      API_PATHS.enhanced.search(query, limit),
      API_PATHS.basic.search(query, limit)
    )
  },

  // 获取股票列表（仅基础版有）
  async getStockList(market?: string, limit = 100) {
    try {
      const response = await httpClient.get(API_PATHS.basic.stockList(market, limit))
      return response.data
    } catch (error) {
      console.error('获取股票列表失败:', error)
      throw error
    }
  },

  // 获取市场概览（仅基础版有）
  async getMarketOverview() {
    try {
      const response = await httpClient.get(API_PATHS.basic.overview())
      return response.data
    } catch (error) {
      console.error('获取市场概览失败:', error)
      throw error
    }
  },

  // 获取板块表现（仅基础版有）
  async getSectorPerformance() {
    try {
      const response = await httpClient.get(API_PATHS.basic.sectors())
      return response.data
    } catch (error) {
      console.error('获取板块表现失败:', error)
      throw error
    }
  },

  // 自选股管理
  async getWatchlist() {
    return callWithFallback(
      API_PATHS.enhanced.watchlist(),
      API_PATHS.basic.watchlist()
    )
  },

  async addToWatchlist(symbol: string) {
    return callWithFallback(
      API_PATHS.enhanced.addWatchlist(symbol),
      API_PATHS.basic.addWatchlist(symbol),
      'POST'
    )
  },

  async removeFromWatchlist(symbol: string) {
    return callWithFallback(
      API_PATHS.enhanced.removeWatchlist(symbol),
      API_PATHS.basic.removeWatchlist(symbol),
      'DELETE'
    )
  },

  // 健康检查
  async checkHealth() {
    return callWithFallback(
      API_PATHS.enhanced.health(),
      API_PATHS.basic.health()
    )
  }
}

// WebSocket主题配置
export const WS_TOPICS = {
  MARKET_ALL: 'market:all',
  MARKET_SYMBOL: (symbol: string) => `market:${symbol}`,
  KLINE: (symbol: string, interval: string) => `market:${symbol}:kline:${interval}`
}

// WebSocket订阅辅助函数
export function createSubscribeMessage(topics: string[]) {
  return {
    type: 'subscribe',
    data: { topics }
  }
}

export function createUnsubscribeMessage(topics: string[]) {
  return {
    type: 'unsubscribe',
    data: { topics }
  }
}

export default marketApi

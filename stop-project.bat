@echo off
chcp 65001 >nul
echo ========================================
echo    量化投资平台 - 停止服务脚本
echo ========================================
echo.

echo 🛑 正在停止所有相关服务...

:: 停止Python/Uvicorn进程
echo 停止后端服务...
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im uvicorn.exe >nul 2>&1

:: 停止Node.js/Vite进程
echo 停止前端服务...
taskkill /f /im node.exe >nul 2>&1

:: 停止可能的CMD窗口
for /f "tokens=2" %%i in ('tasklist /fi "windowtitle eq 量化平台*" /fo csv ^| find /c /v ""') do (
    if %%i gtr 1 (
        taskkill /fi "windowtitle eq 量化平台*" /f >nul 2>&1
    )
)

echo.
echo ✅ 服务停止完成！
echo.
pause

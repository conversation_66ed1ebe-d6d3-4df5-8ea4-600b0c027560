<template>
  <div class="welcome-container">
    <div class="welcome-content">
      <div class="logo-section">
        <img src="/favicon.ico" alt="Logo" class="logo" />
        <h1 class="title">🚀 量化投资平台</h1>
        <p class="subtitle">专业的量化交易可视化平台</p>
      </div>

      <div class="status-section">
        <div class="status-card" :class="{ 'success': systemStatus.frontend }">
          <div class="status-icon">{{ systemStatus.frontend ? '✅' : '❌' }}</div>
          <div class="status-text">
            <h3>前端系统</h3>
            <p>{{ systemStatus.frontend ? '运行正常' : '连接失败' }}</p>
          </div>
        </div>

        <div class="status-card" :class="{ 'success': systemStatus.backend }">
          <div class="status-icon">{{ systemStatus.backend ? '✅' : '❌' }}</div>
          <div class="status-text">
            <h3>后端API</h3>
            <p>{{ systemStatus.backend ? '连接正常' : '连接失败' }}</p>
          </div>
        </div>

        <div class="status-card" :class="{ 'success': systemStatus.websocket }">
          <div class="status-icon">{{ systemStatus.websocket ? '✅' : '❌' }}</div>
          <div class="status-text">
            <h3>WebSocket</h3>
            <p>{{ systemStatus.websocket ? '连接正常' : '连接失败' }}</p>
          </div>
        </div>
      </div>

      <div class="action-section">
        <el-button type="primary" size="large" @click="goToDashboard">
          进入仪表盘
        </el-button>
        <el-button type="default" size="large" @click="goToTest">
          系统测试
        </el-button>
        <el-button type="info" size="large" @click="checkSystem">
          重新检测
        </el-button>
      </div>

      <div class="info-section">
        <div class="info-item">
          <strong>版本:</strong> {{ appVersion }}
        </div>
        <div class="info-item">
          <strong>环境:</strong> {{ environment }}
        </div>
        <div class="info-item">
          <strong>时间:</strong> {{ currentTime }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 响应式数据
const systemStatus = ref({
  frontend: true,
  backend: false,
  websocket: false
})

const appVersion = ref('1.0.0')
const environment = ref(import.meta.env.MODE || 'development')
const currentTime = ref('')

let timeInterval: number | null = null

// 更新当前时间
const updateTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN')
}

// 检查后端API状态
const checkBackendStatus = async () => {
  try {
    const response = await fetch('http://localhost:8000/api/v1/health', {
      method: 'GET',
      timeout: 5000
    })
    
    if (response.ok) {
      systemStatus.value.backend = true
      console.log('✅ 后端API连接正常')
    } else {
      systemStatus.value.backend = false
      console.warn('⚠️ 后端API响应异常')
    }
  } catch (error) {
    systemStatus.value.backend = false
    console.error('❌ 后端API连接失败:', error)
  }
}

// 检查WebSocket状态
const checkWebSocketStatus = () => {
  try {
    const ws = new WebSocket('ws://localhost:8000/api/v1/ws')
    
    ws.onopen = () => {
      systemStatus.value.websocket = true
      console.log('✅ WebSocket连接正常')
      ws.close()
    }
    
    ws.onerror = () => {
      systemStatus.value.websocket = false
      console.error('❌ WebSocket连接失败')
    }
    
    // 5秒后如果还没连接成功，认为失败
    setTimeout(() => {
      if (ws.readyState !== WebSocket.OPEN) {
        systemStatus.value.websocket = false
        ws.close()
      }
    }, 5000)
  } catch (error) {
    systemStatus.value.websocket = false
    console.error('❌ WebSocket连接失败:', error)
  }
}

// 系统检测
const checkSystem = async () => {
  ElMessage.info('正在检测系统状态...')
  
  // 重置状态
  systemStatus.value.backend = false
  systemStatus.value.websocket = false
  
  // 检查各个组件
  await checkBackendStatus()
  checkWebSocketStatus()
  
  // 延迟一下显示结果
  setTimeout(() => {
    const allGood = systemStatus.value.backend && systemStatus.value.websocket
    if (allGood) {
      ElMessage.success('系统状态检测完成，一切正常！')
    } else {
      ElMessage.warning('系统检测完成，部分服务可能不可用')
    }
  }, 2000)
}

// 导航方法
const goToDashboard = () => {
  if (systemStatus.value.backend) {
    router.push('/dashboard')
  } else {
    ElMessage.warning('后端服务未连接，请先检查系统状态')
  }
}

const goToTest = () => {
  router.push('/test')
}

// 生命周期
onMounted(() => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
  
  // 延迟检测，避免页面加载时的阻塞
  setTimeout(() => {
    checkSystem()
  }, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
})
</script>

<style scoped>
.welcome-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.welcome-content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 40px;
  max-width: 800px;
  width: 100%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.logo-section {
  text-align: center;
  margin-bottom: 40px;
}

.logo {
  width: 80px;
  height: 80px;
  margin-bottom: 20px;
}

.title {
  font-size: 2.5em;
  color: #2c3e50;
  margin: 0 0 10px 0;
  font-weight: 600;
}

.subtitle {
  font-size: 1.2em;
  color: #7f8c8d;
  margin: 0;
}

.status-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.status-card {
  display: flex;
  align-items: center;
  padding: 20px;
  border-radius: 12px;
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
}

.status-card.success {
  background: #d4edda;
  border-color: #c3e6cb;
}

.status-icon {
  font-size: 2em;
  margin-right: 15px;
}

.status-text h3 {
  margin: 0 0 5px 0;
  color: #2c3e50;
  font-size: 1.1em;
}

.status-text p {
  margin: 0;
  color: #6c757d;
  font-size: 0.9em;
}

.action-section {
  text-align: center;
  margin-bottom: 30px;
}

.action-section .el-button {
  margin: 0 10px 10px 0;
}

.info-section {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.info-item {
  color: #6c757d;
  font-size: 0.9em;
  margin: 5px;
}

@media (max-width: 768px) {
  .welcome-content {
    padding: 20px;
  }
  
  .title {
    font-size: 2em;
  }
  
  .status-section {
    grid-template-columns: 1fr;
  }
  
  .info-section {
    flex-direction: column;
    text-align: center;
  }
}
</style>

<template>
  <div class="enhanced-historical-data">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1>{{ pageTitle }}</h1>
      <p>{{ pageDescription }}</p>
    </div>

    <!-- 性能指标卡片 -->
    <el-row :gutter="20" class="stats-cards">
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ formatNumber(stats.total_stocks || 0) }}</div>
            <div class="stat-label">可查股票总数</div>
            <div class="stat-growth" :class="{'positive': stats.growth_rate > 0}">
              <el-icon><ArrowUp v-if="stats.growth_rate > 0" /><ArrowDown v-else /></el-icon>
              {{ Math.abs(stats.growth_rate || 0).toFixed(1) }}%
            </div>
          </div>
          <el-icon class="stat-icon"><TrendCharts /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ Object.keys(stats.markets || {}).length }}</div>
            <div class="stat-label">覆盖交易所</div>
            <div class="stat-detail">{{ formatNumber(stats.total_records || 0) }}条记录</div>
          </div>
          <el-icon class="stat-icon"><Location /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ Object.keys(stats.industries || {}).length }}</div>
            <div class="stat-label">涵盖行业</div>
            <div class="stat-detail">分布均匀</div>
          </div>
          <el-icon class="stat-icon"><Grid /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-number">{{ calculateYears(stats.data_range) }}年</div>
            <div class="stat-label">历史数据跨度</div>
            <div class="stat-detail">{{ stats.data_range?.start_date }} 至今</div>
          </div>
          <el-icon class="stat-icon"><Calendar /></el-icon>
        </el-card>
      </el-col>
    </el-row>

    <!-- 功能工具栏 -->
    <el-card class="toolbar-card">
      <div class="toolbar-content">
        <!-- 搜索区域 -->
        <div class="search-section">
          <el-input
            v-model="searchKeyword"
            placeholder="输入股票代码或名称进行搜索"
            @keyup.enter="handleSearch"
            clearable
            size="large"
            class="search-input"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #append>
              <el-button type="primary" @click="handleSearch" :loading="loading">
                搜索
              </el-button>
            </template>
          </el-input>
        </div>

        <!-- 筛选器 -->
        <div class="filter-section">
          <el-select v-model="selectedMarket" placeholder="选择市场" @change="handleFilterChange" clearable>
            <el-option label="全部市场" value="" />
            <el-option label="上交所" value="SH" />
            <el-option label="深交所" value="SZ" />
            <el-option label="北交所" value="BJ" />
          </el-select>

          <el-select v-model="selectedIndustry" placeholder="选择行业" @change="handleFilterChange" clearable>
            <el-option label="全部行业" value="" />
            <el-option
              v-for="industry in industryList"
              :key="industry"
              :label="industry"
              :value="industry"
            />
          </el-select>

          <el-select v-model="sortBy" placeholder="排序方式" @change="handleFilterChange">
            <el-option label="股票代码" value="symbol" />
            <el-option label="股票名称" value="name" />
            <el-option label="数据量" value="total_records" />
            <el-option label="最后更新" value="last_trade_date" />
          </el-select>

          <el-select v-model="sortDirection" @change="handleFilterChange">
            <el-option label="升序" value="asc" />
            <el-option label="降序" value="desc" />
          </el-select>
        </div>

        <!-- 操作按钮 -->
        <div class="action-section">
          <el-button @click="handleReset" icon="RefreshLeft">重置</el-button>
          <el-button @click="showImportDialog = true" type="success" icon="Upload">
            导入数据
          </el-button>
          <el-button @click="handleBatchExport" type="primary" icon="Download">
            批量导出
          </el-button>
          <el-button @click="refreshData" icon="Refresh" :loading="loading">
            刷新
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 股票列表 -->
    <el-card class="stock-list-card">
      <template #header>
        <div class="card-header">
          <span>股票列表</span>
          <div class="header-info">
            <el-tag type="info">共 {{ pagination.total }} 只股票</el-tag>
            <el-tag v-if="cachingInfo.cached" type="success">
              <el-icon><Clock /></el-icon>
              缓存加速
            </el-tag>
          </div>
        </div>
      </template>

      <el-table
        ref="stockTable"
        :data="stockList"
        v-loading="loading"
        @row-click="handleRowClick"
        @selection-change="handleSelectionChange"
        style="width: 100%"
        :row-class-name="getRowClassName"
        height="600"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="symbol" label="代码" width="100" fixed="left" sortable>
          <template #default="{ row }">
            <div class="stock-symbol">
              {{ row.symbol }}
              <el-tag v-if="row.status !== 'L'" size="small" type="warning">
                {{ getStatusText(row.status) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="名称" width="150" fixed="left" show-overflow-tooltip sortable>
          <template #default="{ row }">
            <span class="stock-name">{{ row.name }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="market" label="市场" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="getMarketTagType(row.market)" size="small">
              {{ getMarketName(row.market) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="industry" label="行业" width="120" show-overflow-tooltip sortable />

        <el-table-column prop="total_records" label="数据量" width="100" align="right" sortable>
          <template #default="{ row }">
            <span class="data-count">{{ formatNumber(row.total_records) }}条</span>
          </template>
        </el-table-column>

        <el-table-column prop="first_trade_date" label="起始日期" width="110" align="center" sortable />
        <el-table-column prop="last_trade_date" label="截止日期" width="110" align="center" sortable />

        <el-table-column prop="market_cap" label="市值" width="120" align="right" sortable>
          <template #default="{ row }">
            <span v-if="row.market_cap" class="market-cap">
              {{ formatMoney(row.market_cap) }}
            </span>
            <span v-else class="no-data">-</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click.stop="viewStockData(row)"
              icon="View"
            >
              查看数据
            </el-button>
            <el-button
              type="success"
              size="small"
              @click.stop="viewChart(row)"
              icon="TrendCharts"
            >
              图表分析
            </el-button>
            <el-dropdown @command="(cmd) => handleStockAction(cmd, row)" trigger="click">
              <el-button size="small" icon="More">
                <el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="export">导出数据</el-dropdown-item>
                  <el-dropdown-item command="update">更新数据</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除数据</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>

      <!-- 增强分页 -->
      <div class="pagination-wrapper">
        <div class="pagination-info">
          <span>
            显示第 {{ (pagination.page - 1) * pagination.pageSize + 1 }} -
            {{ Math.min(pagination.page * pagination.pageSize, pagination.total) }} 条，
            共 {{ pagination.total }} 条记录
          </span>
          <el-text type="info" size="small">
            查询耗时: {{ responseTime }}ms
            <span v-if="cachingInfo.cached">（缓存）</span>
          </el-text>
        </div>

        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[20, 50, 100, 200]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 数据详情对话框 -->
    <el-dialog
      v-model="dataDialogVisible"
      :title="`${selectedStock?.name} (${selectedStock?.symbol}) - 历史数据详情`"
      width="95%"
      top="3vh"
      class="data-dialog"
    >
      <StockDataViewer
        v-if="selectedStock"
        :symbol="selectedStock.symbol"
        :name="selectedStock.name"
        @close="dataDialogVisible = false"
      />
    </el-dialog>

    <!-- 数据导入对话框 -->
    <el-dialog
      v-model="showImportDialog"
      title="数据导入"
      width="600px"
    >
      <DataImporter
        @import-success="handleImportSuccess"
        @close="showImportDialog = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'
import {
  TrendCharts, Location, Grid, Calendar, Search, RefreshLeft,
  Upload, Download, Refresh, View, More, ArrowDown, ArrowUp,
  Clock
} from '@element-plus/icons-vue'
import { marketApi } from '@/api/market_unified'
import { unifiedCache, CacheType } from '@/utils/cache'
import StockDataViewer from '@/components/StockDataViewer.vue'
import DataImporter from '@/components/DataImporter.vue'

// 路由
const router = useRouter()

// 响应式数据
const loading = ref(false)
const showImportDialog = ref(false)
const dataDialogVisible = ref(false)

const searchKeyword = ref('')
const selectedMarket = ref('')
const selectedIndustry = ref('')
const sortBy = ref('symbol')
const sortDirection = ref('asc')
const selectedStocks = ref<any[]>([])

const stats = ref<any>({})
const stockList = ref<any[]>([])
const industryList = ref<string[]>([])
const selectedStock = ref<any>(null)
const responseTime = ref(0)

const cachingInfo = ref({
  cached: false,
  hitRate: 0
})

const pagination = reactive({
  page: 1,
  pageSize: 50,
  total: 0,
  totalPages: 0
})

// 计算属性
const calculateYears = (range: any) => {
  if (!range || !range.start_date || !range.end_date) return 0
  const start = new Date(range.start_date)
  const end = new Date(range.end_date)
  return Math.ceil((end.getTime() - start.getTime()) / (365 * 24 * 60 * 60 * 1000))
}

// 数据加载方法
const loadStats = async () => {
  try {
    const startTime = Date.now()
    const response = await marketApi.getHistoricalStats()
    responseTime.value = Date.now() - startTime

    if (response.data && response.data.success) {
      stats.value = response.data.data
      cachingInfo.value.cached = response.data.cached || false

      // 提取行业列表
      if (stats.value.industries) {
        industryList.value = Object.keys(stats.value.industries).sort()
      }

      console.log('📊 加载统计数据成功:', stats.value)
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')

    // 使用默认数据
    stats.value = {
      total_stocks: 0,
      markets: {},
      industries: {},
      data_range: null,
      total_records: 0
    }
  }
}

const loadStockList = async () => {
  loading.value = true
  console.log('🚀 开始加载股票列表...')

  try {
    const startTime = Date.now()
    const params = {
      market: selectedMarket.value || undefined,
      industry: selectedIndustry.value || undefined,
      page: pagination.page,
      page_size: pagination.pageSize,
      order_by: sortBy.value,
      order_direction: sortDirection.value
    }

    // 移除空值参数
    Object.keys(params).forEach(key => {
      if (params[key] === undefined) {
        delete params[key]
      }
    })

    const response = await marketApi.getHistoricalStockList(params)
    responseTime.value = Date.now() - startTime

    if (response.data && response.data.success) {
      const result = response.data.data
      stockList.value = result.stocks || []
      pagination.total = result.total || 0
      pagination.totalPages = result.total_pages || 0
      cachingInfo.value.cached = response.data.cached || false

      console.log('📊 加载股票列表成功:', stockList.value.length, '只股票')
    }
  } catch (error) {
    console.error('加载股票列表失败:', error)
    ElMessage.error('加载股票列表失败')
    stockList.value = []
    pagination.total = 0
  } finally {
    loading.value = false
  }
}

// 搜索和筛选
const handleSearch = async () => {
  if (searchKeyword.value.trim()) {
    // 使用搜索API
    try {
      loading.value = true
      const startTime = Date.now()

      const response = await marketApi.searchHistoricalStocks(searchKeyword.value, 50)

      responseTime.value = Date.now() - startTime

      if (response.data && response.data.success) {
        stockList.value = response.data.data
        pagination.total = response.data.data.length
        pagination.page = 1
        cachingInfo.value.cached = response.data.cached || false

        console.log(`搜索 "${searchKeyword.value}" 成功:`, stockList.value.length, '个结果')
      }
    } catch (error) {
      console.error('搜索失败:', error)
      ElMessage.error('搜索失败')
    } finally {
      loading.value = false
    }
  } else {
    pagination.page = 1
    await loadStockList()
  }
}

const handleFilterChange = () => {
  pagination.page = 1
  loadStockList()
}

const handleReset = () => {
  searchKeyword.value = ''
  selectedMarket.value = ''
  selectedIndustry.value = ''
  sortBy.value = 'symbol'
  sortDirection.value = 'asc'
  pagination.page = 1
  loadStockList()
}

const refreshData = async () => {
  // 清除缓存
  await unifiedCache.clear(CacheType.HISTORICAL)
  await loadStats()
  await loadStockList()
  ElMessage.success('数据已刷新')
}

// 分页处理
const handlePageChange = (page: number) => {
  pagination.page = page
  loadStockList()
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.page = 1
  loadStockList()
}

// 表格操作
const handleRowClick = (row: any) => {
  selectedStock.value = row
  dataDialogVisible.value = true
}

const handleSelectionChange = (selection: any[]) => {
  selectedStocks.value = selection
}

const viewStockData = (row: any) => {
  selectedStock.value = row
  dataDialogVisible.value = true
}

const viewChart = (row: any) => {
  router.push(`/market/${row.symbol}`)
}

const handleStockAction = async (command: string, row: any) => {
  switch (command) {
    case 'export':
      await exportSingleStock(row)
      break
    case 'update':
      await updateStockData(row)
      break
    case 'delete':
      await deleteStockData(row)
      break
  }
}

// 导出功能
const exportSingleStock = async (stock: any) => {
  try {
    loading.value = true

    const response = await httpClient.post('/market/historical/export', {
      symbols: [stock.symbol],
      format: 'csv'
    })

    if (response.data && response.data.success) {
      downloadFile(response.data.content, response.data.filename, response.data.mime_type)
      ElMessage.success(`成功导出 ${stock.name} 数据`)
    }
  } catch (error) {
    console.error('导出失败:', error)
    ElMessage.error('导出数据失败')
  } finally {
    loading.value = false
  }
}

const handleBatchExport = async () => {
  if (selectedStocks.value.length === 0) {
    ElMessage.warning('请先选择要导出的股票')
    return
  }

  try {
    loading.value = true

    const symbols = selectedStocks.value.map(stock => stock.symbol)
    const response = await httpClient.post('/market/historical/export', {
      symbols,
      format: 'excel'
    })

    if (response.data && response.data.success) {
      downloadFile(response.data.content, response.data.filename, response.data.mime_type)
      ElMessage.success(`成功导出 ${symbols.length} 只股票数据`)
    }
  } catch (error) {
    console.error('批量导出失败:', error)
    ElMessage.error('批量导出失败')
  } finally {
    loading.value = false
  }
}

const downloadFile = (content: string, filename: string, mimeType: string) => {
  const binaryString = atob(content)
  const bytes = new Uint8Array(binaryString.length)
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i)
  }

  const blob = new Blob([bytes], { type: mimeType })
  const url = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = filename

  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(url)
}

// 数据管理
const updateStockData = async (stock: any) => {
  ElMessage.info('数据更新功能开发中...')
}

const deleteStockData = async (stock: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除 ${stock.name}(${stock.symbol}) 的历史数据吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    ElMessage.info('删除功能开发中...')
  } catch {
    // 用户取消
  }
}

const handleImportSuccess = () => {
  showImportDialog.value = false
  refreshData()
  ElMessage.success('数据导入成功')
}

// 工具函数
const getMarketName = (market: string) => {
  const marketNames: Record<string, string> = {
    'SH': '上交所',
    'SZ': '深交所',
    'BJ': '北交所'
  }
  return marketNames[market] || market
}

const getMarketTagType = (market: string) => {
  switch (market) {
    case 'SH': return 'danger'
    case 'SZ': return 'success'
    case 'BJ': return 'warning'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'L': '正常',
    'D': '退市',
    'P': '暂停'
  }
  return statusMap[status] || status
}

const getRowClassName = ({ row }: { row: any }) => {
  if (row.status === 'D') return 'row-delisted'
  if (row.status === 'P') return 'row-suspended'
  return ''
}

const formatNumber = (num: number) => {
  if (!num) return '0'
  return num.toLocaleString()
}

const formatMoney = (amount: number | string) => {
  const num = parseFloat(amount as string)
  if (!num || isNaN(num)) return '0'

  if (num >= 1e12) {
    return (num / 1e12).toFixed(2) + '万亿'
  } else if (num >= 1e8) {
    return (num / 1e8).toFixed(2) + '亿'
  } else if (num >= 1e4) {
    return (num / 1e4).toFixed(2) + '万'
  }
  return num.toLocaleString()
}

// 生命周期
onMounted(() => {
  loadStats()
  loadStockList()
})
</script>

<style scoped>
.enhanced-historical-data {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
  text-align: center;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 28px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 16px;
}

/* 统计卡片样式 */
.stats-cards {
  margin-bottom: 20px;
}

.stat-card {
  position: relative;
  overflow: hidden;
  transition: all 0.3s;
  border: 1px solid #e4e7ed;
}

.stat-card:hover {
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.stat-content {
  position: relative;
  z-index: 2;
  padding: 20px;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 4px;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: #909399;
  margin-bottom: 4px;
}

.stat-detail {
  font-size: 12px;
  color: #C0C4CC;
}

.stat-growth {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #F56C6C;
}

.stat-growth.positive {
  color: #67C23A;
}

.stat-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32px;
  color: #f0f0f0;
  z-index: 1;
}

/* 工具栏样式 */
.toolbar-card {
  margin-bottom: 20px;
}

.toolbar-content {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  flex-wrap: wrap;
}

.search-section {
  flex: 1;
  min-width: 300px;
}

.search-input {
  font-size: 16px;
}

.filter-section {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.action-section {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

/* 表格样式 */
.stock-list-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-info {
  display: flex;
  gap: 10px;
  align-items: center;
}

.stock-symbol {
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #409eff;
}

.stock-name {
  font-weight: 500;
}

.data-count {
  color: #909399;
  font-size: 12px;
}

.market-cap {
  font-weight: 500;
  color: #E6A23C;
}

.no-data {
  color: #c0c4cc;
}

/* 表格行样式 */
:deep(.row-delisted) {
  background-color: #fef0f0;
  color: #909399;
}

:deep(.row-suspended) {
  background-color: #fdf6ec;
}

/* 分页样式 */
.pagination-wrapper {
  margin-top: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 0;
  border-top: 1px solid #f0f0f0;
}

.pagination-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  color: #909399;
  font-size: 14px;
}

/* 对话框样式 */
.data-dialog {
  border-radius: 8px;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .enhanced-historical-data {
    padding: 10px;
  }

  .toolbar-content {
    flex-direction: column;
    gap: 15px;
  }

  .search-section {
    min-width: auto;
  }

  .filter-section,
  .action-section {
    width: 100%;
    justify-content: flex-start;
  }

  .pagination-wrapper {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
}
</style>

"""
增强历史数据API路由
基于数据库存储，提供高性能的历史数据查询
"""

from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import logging

from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.database import get_db
from app.core.dependencies import get_current_user
from app.db.models.user import User
from app.services.enhanced_historical_service import enhanced_historical_service
from app.core.unified_cache import unified_cache, CacheType

router = APIRouter(tags=["增强历史数据"])
logger = logging.getLogger(__name__)


@router.get("/historical/stocks", summary="获取历史股票列表")
async def get_historical_stock_list(
    market: Optional[str] = Query(None, description="市场代码 (SH/SZ/BJ)"),
    industry: Optional[str] = Query(None, description="行业分类"),
    status: str = Query("L", description="股票状态"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(50, ge=1, le=200, description="每页数量"),
    order_by: str = Query("symbol", description="排序字段"),
    order_direction: str = Query("asc", description="排序方向 asc/desc"),
    use_cache: bool = Query(True, description="是否使用缓存"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    获取历史数据中的股票列表
    基于数据库存储，支持高效查询和分页
    """
    try:
        # 构建缓存键
        cache_key_parts = [
            market or "all",
            industry or "all", 
            status,
            str(page),
            str(page_size),
            order_by,
            order_direction
        ]
        
        # 检查缓存
        if use_cache:
            cached_data = await unified_cache.get(CacheType.HISTORICAL, *cache_key_parts)
            if cached_data is not None:
                logger.debug("从缓存获取股票列表")
                return {
                    "success": True,
                    "data": cached_data,
                    "cached": True,
                    "timestamp": datetime.now().isoformat()
                }
        
        # 从数据库查询
        result = await enhanced_historical_service.get_stock_list(
            db=db,
            market=market,
            industry=industry,
            status=status,
            page=page,
            page_size=page_size,
            order_by=order_by,
            order_direction=order_direction
        )
        
        # 缓存结果
        if use_cache:
            await unified_cache.set(CacheType.HISTORICAL, result, *cache_key_parts)
        
        return {
            "success": True,
            "data": result,
            "cached": False,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取历史股票列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取股票列表失败: {str(e)}")


@router.get("/historical/stocks/{symbol}/data", summary="获取股票历史数据")
async def get_historical_stock_data(
    symbol: str,
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    fields: Optional[str] = Query(None, description="指定字段，逗号分隔"),
    limit: Optional[int] = Query(None, ge=1, le=10000, description="限制条数"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(100, ge=1, le=1000, description="每页数量"),
    use_cache: bool = Query(True, description="是否使用缓存"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    获取指定股票的历史数据
    支持日期范围筛选、字段选择和分页
    """
    try:
        # 解析字段列表
        field_list = fields.split(',') if fields else None
        
        # 构建缓存键
        cache_key_parts = [
            symbol,
            start_date.strftime('%Y%m%d') if start_date else 'all',
            end_date.strftime('%Y%m%d') if end_date else 'all',
            '_'.join(field_list) if field_list else 'all',
            str(limit) if limit else 'all',
            str(page),
            str(page_size)
        ]
        
        # 检查缓存
        if use_cache:
            cached_data = await unified_cache.get(CacheType.HISTORICAL, *cache_key_parts)
            if cached_data is not None:
                logger.debug(f"从缓存获取股票 {symbol} 数据")
                return {
                    "success": True,
                    "data": cached_data,
                    "cached": True,
                    "symbol": symbol,
                    "timestamp": datetime.now().isoformat()
                }
        
        # 从数据库查询
        # 如果有分页需求，需要调整limit
        actual_limit = limit
        if not limit:
            # 计算分页的实际限制
            actual_limit = page * page_size
        
        data = await enhanced_historical_service.get_stock_data(
            db=db,
            symbol=symbol,
            start_date=start_date,
            end_date=end_date,
            fields=field_list,
            limit=actual_limit,
            use_cache=False  # 这里不使用服务层的缓存，统一使用API层缓存
        )
        
        if data is None:
            raise HTTPException(status_code=404, detail="未找到股票历史数据")
        
        # 分页处理
        total_count = len(data)
        start_idx = (page - 1) * page_size
        end_idx = start_idx + page_size
        page_data = data[start_idx:end_idx]
        
        result = {
            "data": page_data,
            "total": total_count,
            "page": page,
            "page_size": page_size,
            "total_pages": (total_count + page_size - 1) // page_size
        }
        
        # 缓存结果
        if use_cache:
            await unified_cache.set(CacheType.HISTORICAL, result, *cache_key_parts)
        
        return {
            "success": True,
            "data": result,
            "cached": False,
            "symbol": symbol,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取股票 {symbol} 历史数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取历史数据失败: {str(e)}")


@router.get("/historical/search", summary="搜索历史股票数据")
async def search_historical_stocks(
    keyword: str = Query(..., min_length=1, description="搜索关键词"),
    limit: int = Query(20, ge=1, le=50, description="返回条数"),
    use_cache: bool = Query(True, description="是否使用缓存"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    在历史数据中搜索股票
    支持股票代码和名称搜索，使用统一缓存
    """
    try:
        # 构建缓存键
        cache_key_parts = [keyword.lower(), str(limit)]
        
        # 检查缓存
        if use_cache:
            cached_data = await unified_cache.get(CacheType.SEARCH, *cache_key_parts)
            if cached_data is not None:
                logger.debug(f"从缓存获取搜索结果: {keyword}")
                return {
                    "success": True,
                    "data": cached_data,
                    "cached": True,
                    "keyword": keyword,
                    "timestamp": datetime.now().isoformat()
                }
        
        # 从数据库搜索
        results = await enhanced_historical_service.search_stocks(
            db=db,
            keyword=keyword,
            limit=limit
        )
        
        # 缓存结果
        if use_cache:
            await unified_cache.set(CacheType.SEARCH, results, *cache_key_parts)
        
        return {
            "success": True,
            "data": results,
            "count": len(results),
            "cached": False,
            "keyword": keyword,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"搜索股票失败: {e}")
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")


@router.get("/historical/stats", summary="获取历史数据统计")
async def get_historical_stats(
    use_cache: bool = Query(True, description="是否使用缓存"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    获取历史数据的统计信息
    包括股票数量、市场分布、行业分布等
    """
    try:
        # 构建缓存键
        cache_key_parts = ["market_stats"]
        
        # 检查缓存
        if use_cache:
            cached_data = await unified_cache.get(CacheType.HISTORICAL, *cache_key_parts)
            if cached_data is not None:
                logger.debug("从缓存获取市场统计")
                return {
                    "success": True,
                    "data": cached_data,
                    "cached": True,
                    "timestamp": datetime.now().isoformat()
                }
        
        # 从数据库查询
        stats = await enhanced_historical_service.get_market_statistics(db=db)
        
        # 缓存结果
        if use_cache:
            await unified_cache.set(CacheType.HISTORICAL, stats, *cache_key_parts)
        
        return {
            "success": True,
            "data": stats,
            "cached": False,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取市场统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.post("/historical/import-csv", summary="导入CSV历史数据")
async def import_csv_data(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(..., description="CSV文件"),
    symbol: str = Query(..., description="股票代码"),
    name: Optional[str] = Query(None, description="股票名称"),
    update_mode: str = Query("replace", description="更新模式 replace/append"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    从CSV文件导入历史数据到数据库
    支持后台任务处理大文件
    """
    try:
        # 验证文件类型
        if not file.filename.endswith('.csv'):
            raise HTTPException(status_code=400, detail="只支持CSV文件")
        
        # 保存上传文件到临时目录
        import tempfile
        import os
        
        temp_dir = tempfile.mkdtemp()
        temp_file_path = os.path.join(temp_dir, file.filename)
        
        with open(temp_file_path, 'wb') as temp_file:
            content = await file.read()
            temp_file.write(content)
        
        # 启动后台任务处理导入
        background_tasks.add_task(
            process_csv_import,
            temp_file_path,
            symbol,
            name or f"股票{symbol}",
            update_mode
        )
        
        return {
            "success": True,
            "message": f"CSV文件上传成功，正在后台处理导入 {symbol}",
            "file_size": len(content),
            "symbol": symbol,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"CSV导入失败: {e}")
        raise HTTPException(status_code=500, detail=f"导入失败: {str(e)}")


async def process_csv_import(
    file_path: str,
    symbol: str,
    name: str,
    update_mode: str
):
    """
    处理CSV导入的后台任务
    """
    try:
        async for db in get_db():
            result = await enhanced_historical_service.import_csv_data(
                db=db,
                csv_file_path=file_path,
                symbol=symbol,
                name=name,
                update_mode=update_mode
            )
            
            logger.info(f"CSV导入完成: {symbol}, 结果: {result}")
            
            # 清理临时文件
            import os
            if os.path.exists(file_path):
                os.remove(file_path)
                os.rmdir(os.path.dirname(file_path))
            
            # 清除相关缓存
            await unified_cache.clear(CacheType.HISTORICAL)
            
            break
            
    except Exception as e:
        logger.error(f"后台CSV导入失败: {e}")


@router.post("/historical/export", summary="导出历史数据")
async def export_historical_data(
    symbols: List[str] = Query(..., description="股票代码列表"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    format: str = Query("csv", description="导出格式 csv/excel"),
    fields: Optional[str] = Query(None, description="导出字段，逗号分隔"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    导出历史数据
    支持多种格式和字段选择
    """
    try:
        # 解析字段列表
        field_list = fields.split(',') if fields else None
        
        # 收集所有股票数据
        all_data = []
        for symbol in symbols:
            data = await enhanced_historical_service.get_stock_data(
                db=db,
                symbol=symbol,
                start_date=start_date,
                end_date=end_date,
                fields=field_list,
                use_cache=True
            )
            
            if data:
                # 为每条记录添加股票代码
                for record in data:
                    record['股票代码'] = symbol
                all_data.extend(data)
        
        if not all_data:
            raise HTTPException(status_code=404, detail="没有找到匹配的数据")
        
        # 生成导出文件
        import pandas as pd
        import base64
        from io import BytesIO
        
        df = pd.DataFrame(all_data)
        
        if format.lower() == 'csv':
            # CSV导出
            output = BytesIO()
            df.to_csv(output, index=False, encoding='utf-8-sig')
            content = output.getvalue()
            filename = f"历史数据_{'_'.join(symbols[:3])}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            mime_type = "text/csv"
            
        elif format.lower() == 'excel':
            # Excel导出
            output = BytesIO()
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name='历史数据', index=False)
            content = output.getvalue()
            filename = f"历史数据_{'_'.join(symbols[:3])}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            mime_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            
        else:
            raise HTTPException(status_code=400, detail="不支持的导出格式")
        
        # 编码为base64
        content_b64 = base64.b64encode(content).decode('utf-8')
        
        return {
            "success": True,
            "content": content_b64,
            "filename": filename,
            "mime_type": mime_type,
            "rows": len(all_data),
            "symbols": symbols,
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"导出历史数据失败: {e}")
        raise HTTPException(status_code=500, detail=f"导出失败: {str(e)}")


@router.delete("/historical/cache", summary="清除历史数据缓存")
async def clear_historical_cache(
    cache_type: Optional[str] = Query(None, description="缓存类型"),
    current_user: User = Depends(get_current_user),
):
    """
    清除历史数据缓存
    """
    try:
        if cache_type:
            cache_enum = getattr(CacheType, cache_type.upper(), None)
            if cache_enum:
                cleared = await unified_cache.clear(cache_enum)
            else:
                raise HTTPException(status_code=400, detail="无效的缓存类型")
        else:
            cleared = await unified_cache.clear(CacheType.HISTORICAL)
        
        return {
            "success": True,
            "message": f"已清除 {cleared} 个缓存项",
            "cache_type": cache_type or "historical",
            "timestamp": datetime.now().isoformat()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"清除缓存失败: {e}")
        raise HTTPException(status_code=500, detail=f"清除缓存失败: {str(e)}")


@router.get("/historical/cache/stats", summary="获取缓存统计信息")
async def get_cache_stats(
    current_user: User = Depends(get_current_user),
):
    """
    获取缓存统计信息
    """
    try:
        stats = await unified_cache.get_stats()
        
        return {
            "success": True,
            "data": stats,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取缓存统计失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取统计失败: {str(e)}")
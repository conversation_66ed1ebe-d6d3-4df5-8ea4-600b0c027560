import type { RouteRecordRaw } from 'vue-router'

const marketRoutes: RouteRecordRaw[] = [
  {
    path: '/market',
    name: 'market',
    component: () => import('@/views/Market/RealTimeMarketV2.vue'),
    meta: {
      title: '行情中心'
    }
  },
  {
    path: '/market/realtime',
    name: 'realtime-market',
    component: () => import('@/views/Market/RealTimeMarketV2.vue'),
    meta: {
      title: '实时行情'
    }
  },
  {
    path: '/market/realtime-v1',
    name: 'realtime-market-v1',
    component: () => import('@/views/Market/RealTimeMarket.vue'),
    meta: {
      title: '实时行情(旧版)'
    }
  },
  {
    path: '/market/astock-realtime',
    name: 'astock-realtime',
    component: () => import('@/views/Market/AStockRealTime.vue'),
    meta: {
      title: 'A股实时行情'
    }
  },
  {
    path: '/market/historical',
    name: 'historical-data',
    component: () => import('@/views/Market/EnhancedHistoricalData.vue'),
    meta: {
      title: '历史数据中心'
    }
  },
  {
    path: '/market/websocket-monitor',
    name: 'websocket-monitor',
    component: () => import('@/views/Market/WebSocketMonitor.vue'),
    meta: {
      title: 'WebSocket监控'
    }
  },
  {
    path: '/market/:symbol',
    name: 'stock-detail',
    component: () => import('@/views/Market/StockDetail.vue'),
    meta: {
      title: '股票详情'
    },
    props: true
  }
]

export default marketRoutes

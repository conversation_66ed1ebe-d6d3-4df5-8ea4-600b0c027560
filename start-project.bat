@echo off
echo ========================================
echo    Quant Platform - Startup Script
echo ========================================
echo.

:: Check Python
echo [1/5] Checking Python...
where python >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python not found
    pause
    exit /b 1
)

:: Check pnpm
where pnpm >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: pnpm not found. Install with: npm install -g pnpm
    pause
    exit /b 1
)

echo OK: Environment check passed

:: Setup backend
echo.
echo [2/5] Setting up backend...
cd /d "%~dp0backend"
if not exist "venv" (
    echo Creating Python virtual environment...
    python -m venv venv
)

echo Installing backend dependencies...
call venv\Scripts\activate.bat
pip install -r requirements.txt >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Backend dependencies failed
    pause
    exit /b 1
)
echo OK: Backend setup complete

:: Setup frontend
echo.
echo [3/5] Setting up frontend...
cd /d "%~dp0frontend"
if not exist "node_modules" (
    echo Installing frontend dependencies...
    pnpm install
    if %errorlevel% neq 0 (
        echo ERROR: Frontend dependencies failed
        pause
        exit /b 1
    )
) else (
    echo OK: Frontend dependencies exist
)

:: Start backend
echo.
echo [4/5] Starting backend...
cd /d "%~dp0backend"
start "Backend" cmd /k "call venv\Scripts\activate.bat && python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload"

:: Wait for backend
echo Waiting for backend to start...
timeout /t 5 /nobreak >nul

:: Start frontend
echo.
echo [5/5] Starting frontend...
cd /d "%~dp0frontend"
start "Frontend" cmd /k "pnpm run dev"

echo.
echo ========================================
echo SUCCESS: Project started!
echo.
echo Frontend: http://localhost:5173
echo Backend:  http://localhost:8000
echo API Docs: http://localhost:8000/docs
echo.
echo Press any key to close...
echo ========================================
pause >nul

<script setup lang="ts">
import { RouterView } from 'vue-router'
import { ref } from 'vue'

// 简化版本，移除可能有问题的导入
const isDarkMode = ref(false)
</script>

<template>
  <div id="app-wrapper" :class="{ 'dark': isDarkMode }">
    <RouterView />
  </div>
</template>

<style lang="scss">
#app-wrapper {
  width: 100%;
  min-height: 100vh;
  height: auto !important;
  overflow: visible;
}

/* 全局布局修复 */
:deep(.default-layout) {
  height: auto !important;
  min-height: 100vh !important;
}

:deep(.layout-content) {
  height: auto !important;
  overflow: visible !important;
}

:deep(.market-content) {
  height: auto !important;
  overflow-x: hidden !important;
  overflow-y: visible !important;
}
</style>

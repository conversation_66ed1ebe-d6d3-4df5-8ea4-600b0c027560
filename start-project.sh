#!/bin/bash

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================"
echo -e "    量化投资平台 - 项目启动脚本"
echo -e "========================================${NC}"
echo

# 检查是否安装了必要的工具
echo -e "${YELLOW}[1/5] 检查环境依赖...${NC}"

if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo -e "${RED}❌ Python 未安装${NC}"
    exit 1
fi

if ! command -v pnpm &> /dev/null; then
    echo -e "${RED}❌ pnpm 未安装，请先安装 pnpm: npm install -g pnpm${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 环境检查通过${NC}"

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# 检查后端依赖
echo
echo -e "${YELLOW}[2/5] 检查后端依赖...${NC}"
cd "$SCRIPT_DIR/backend"

# 确定Python命令
PYTHON_CMD="python3"
if ! command -v python3 &> /dev/null; then
    PYTHON_CMD="python"
fi

if [ ! -d "venv" ]; then
    echo -e "${BLUE}📦 创建Python虚拟环境...${NC}"
    $PYTHON_CMD -m venv venv
fi

echo -e "${BLUE}📦 激活虚拟环境并安装依赖...${NC}"
source venv/bin/activate
pip install -r requirements.txt > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 后端依赖安装失败${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 后端依赖检查完成${NC}"

# 检查前端依赖
echo
echo -e "${YELLOW}[3/5] 检查前端依赖...${NC}"
cd "$SCRIPT_DIR/frontend"

if [ ! -d "node_modules" ]; then
    echo -e "${BLUE}📦 安装前端依赖...${NC}"
    pnpm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 前端依赖安装失败${NC}"
        exit 1
    fi
else
    echo -e "${GREEN}✅ 前端依赖已存在${NC}"
fi

# 启动后端服务
echo
echo -e "${YELLOW}[4/5] 启动后端服务...${NC}"
cd "$SCRIPT_DIR/backend"

# 在后台启动后端
nohup bash -c "source venv/bin/activate && python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload" > backend.log 2>&1 &
BACKEND_PID=$!

echo -e "${BLUE}等待后端服务启动...${NC}"
sleep 5

# 检查后端是否启动成功
if kill -0 $BACKEND_PID 2>/dev/null; then
    echo -e "${GREEN}✅ 后端服务启动成功 (PID: $BACKEND_PID)${NC}"
else
    echo -e "${RED}❌ 后端服务启动失败${NC}"
    exit 1
fi

# 启动前端服务
echo
echo -e "${YELLOW}[5/5] 启动前端服务...${NC}"
cd "$SCRIPT_DIR/frontend"

# 在后台启动前端
nohup pnpm run dev > frontend.log 2>&1 &
FRONTEND_PID=$!

echo -e "${BLUE}等待前端服务启动...${NC}"
sleep 3

# 检查前端是否启动成功
if kill -0 $FRONTEND_PID 2>/dev/null; then
    echo -e "${GREEN}✅ 前端服务启动成功 (PID: $FRONTEND_PID)${NC}"
else
    echo -e "${RED}❌ 前端服务启动失败${NC}"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

echo
echo -e "${GREEN}========================================"
echo -e "✅ 项目启动完成！"
echo
echo -e "🌐 前端地址: http://localhost:5173"
echo -e "🔧 后端地址: http://localhost:8000"
echo -e "📚 API文档: http://localhost:8000/docs"
echo
echo -e "📝 日志文件:"
echo -e "   后端: $SCRIPT_DIR/backend/backend.log"
echo -e "   前端: $SCRIPT_DIR/frontend/frontend.log"
echo
echo -e "🛑 停止服务:"
echo -e "   kill $BACKEND_PID $FRONTEND_PID"
echo -e "========================================${NC}"

# 保存PID到文件，方便后续停止
echo "$BACKEND_PID $FRONTEND_PID" > "$SCRIPT_DIR/.pids"

echo -e "${YELLOW}按 Ctrl+C 停止所有服务...${NC}"

# 捕获中断信号，清理进程
trap 'echo -e "\n${YELLOW}正在停止服务...${NC}"; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; rm -f "$SCRIPT_DIR/.pids"; exit 0' INT

# 等待用户中断
while true; do
    sleep 1
done

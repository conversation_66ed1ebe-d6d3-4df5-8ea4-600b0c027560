import { createApp } from 'vue'
import { createPinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'

// 创建最简单的Vue应用，用于调试
const SimpleApp = {
  template: `
    <div style="padding: 20px; font-family: Arial, sans-serif;">
      <h1 style="color: #409EFF;">🎉 Vue应用正常运行！</h1>
      <p><strong>当前时间:</strong> {{ currentTime }}</p>
      <p><strong>应用状态:</strong> 已成功挂载</p>
      <button @click="count++" style="padding: 8px 16px; background: #409EFF; color: white; border: none; border-radius: 4px; margin: 5px;">
        点击测试 ({{ count }})
      </button>
      <button @click="testBackend" style="padding: 8px 16px; background: #67C23A; color: white; border: none; border-radius: 4px; margin: 5px;">
        测试后端连接
      </button>
      <button @click="testNavigation" style="padding: 8px 16px; background: #E6A23C; color: white; border: none; border-radius: 4px; margin: 5px;">
        测试路由导航
      </button>
      
      <div v-if="backendStatus" style="margin: 10px 0; padding: 10px; border-radius: 4px;" :style="{ background: backendStatus.success ? '#d4edda' : '#f8d7da', color: backendStatus.success ? '#155724' : '#721c24' }">
        <strong>后端状态:</strong> {{ backendStatus.message }}
      </div>
      
      <div style="margin-top: 20px;">
        <h3>导航测试</h3>
        <router-link to="/" style="margin: 5px; padding: 5px 10px; background: #f0f0f0; text-decoration: none; border-radius: 3px;">首页</router-link>
        <router-link to="/market" style="margin: 5px; padding: 5px 10px; background: #f0f0f0; text-decoration: none; border-radius: 3px;">市场数据</router-link>
        <router-link to="/simple-test" style="margin: 5px; padding: 5px 10px; background: #f0f0f0; text-decoration: none; border-radius: 3px;">简单测试</router-link>
      </div>
      
      <router-view></router-view>
    </div>
  `,
  data() {
    return {
      count: 0,
      currentTime: new Date().toLocaleString(),
      backendStatus: null
    }
  },
  async mounted() {
    setInterval(() => {
      this.currentTime = new Date().toLocaleString()
    }, 1000)
    console.log('✅ 简化应用启动成功')
    
    // 自动测试后端连接
    this.testBackend()
  },
  methods: {
    async testBackend() {
      try {
        const response = await fetch('http://localhost:8000/health')
        if (response.ok) {
          const data = await response.json()
          this.backendStatus = {
            success: true,
            message: `连接正常 - ${data.status}`
          }
        } else {
          this.backendStatus = {
            success: false,
            message: `连接失败 - ${response.status}`
          }
        }
      } catch (error) {
        this.backendStatus = {
          success: false,
          message: `连接错误 - ${error.message}`
        }
      }
    },
    testNavigation() {
      console.log('当前路由:', this.$route.path)
      this.$router.push('/simple-test')
    }
  }
}

// 创建简单的路由
const SimpleTestComponent = {
  template: `
    <div style="background: #f0f9ff; padding: 15px; margin: 10px 0; border-radius: 5px;">
      <h3>🧪 简单测试组件</h3>
      <p>这是一个独立的测试组件，用于验证路由是否正常工作。</p>
      <button @click="$router.go(-1)" style="padding: 5px 10px; background: #409EFF; color: white; border: none; border-radius: 3px;">
        返回
      </button>
    </div>
  `
}

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      component: { template: '<div style="background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px;"><h3>首页内容</h3><p>欢迎使用量化投资平台</p></div>' }
    },
    {
      path: '/market',
      component: { template: '<div style="background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px;"><h3>市场数据</h3><p>市场数据组件加载成功</p></div>' }
    },
    {
      path: '/simple-test',
      component: SimpleTestComponent
    }
  ]
})

// 创建Pinia
const pinia = createPinia()

// 创建应用
const app = createApp(SimpleApp)

// 配置应用
app.use(pinia)
app.use(router)

// 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue错误:', err, info)
}

// 挂载应用
app.mount('#app')

console.log('🔧 简化版应用已挂载')
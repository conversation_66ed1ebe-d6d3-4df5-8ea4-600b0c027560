# API路径迁移指南

## 📅 更新时间
2024年更新 - 量化投资平台API统一整改

## 🎯 整改目标
统一和简化API路径，消除重复和不一致问题

## 📊 已完成的API整改

### 1. 统一市场数据API
- ✅ **保留唯一**: `backend/app/api/v1/unified_market.py`
- ❌ **已移除**: `backend/app/api/v1/market_unified.py` → `deprecated/`
- ❌ **已移除**: `backend/app/api/v1/market_data.py` → `deprecated/`

### 2. 统一WebSocket服务
- ✅ **保留唯一**: `backend/app/api/v1/websocket_unified.py`
- ❌ **已移除**: `backend/app/api/v1/websocket_enhanced.py` → `deprecated/`
- ❌ **已移除**: `backend/app/api/v1/websocket_market.py` → `deprecated/`

### 3. 统一历史数据API
- ✅ **保留**: `backend/app/api/v1/historical_data.py` (通过统一市场API访问)
- ❌ **已移除**: `backend/app/api/v1/enhanced_historical_data.py` → `deprecated/`

### 4. 前端API服务
- ✅ **保留唯一**: `frontend/src/api/market_unified.ts`
- ✅ **保留唯一**: `frontend/src/services/market-websocket.service.ts`
- ❌ **已移除**: `frontend/src/api/marketApiAdapter.ts` → `deprecated/`
- ❌ **已移除**: `frontend/src/services/websocket.service.ts` → `deprecated/`
- ❌ **已移除**: `frontend/src/services/enhanced-websocket.service.ts` → `deprecated/`

## 🔧 API路径标准化

### 统一访问路径
所有市场相关API现在统一通过以下路径访问：

```
/api/v1/market/*
```

### 具体端点映射

#### 实时行情
```
GET /api/v1/market/overview           # 市场概览
GET /api/v1/market/quotes/realtime   # 实时行情
GET /api/v1/market/quotes/{symbol}   # 单个股票行情
GET /api/v1/market/quotes/{symbol}/orderbook  # 五档盘口
GET /api/v1/market/quotes/{symbol}/trades     # 逐笔成交
```

#### K线数据
```
GET /api/v1/market/kline/{symbol}     # K线数据
```

#### 历史数据
```
GET /api/v1/market/historical/stats           # 历史数据统计
GET /api/v1/market/historical/stocks          # 历史股票列表
GET /api/v1/market/historical/search          # 搜索历史股票
GET /api/v1/market/historical/data/{symbol}   # 历史数据详情
POST /api/v1/market/historical/export         # 导出历史数据
```

#### 排行榜和分析
```
GET /api/v1/market/rankings/{type}    # 排行榜
GET /api/v1/market/sectors            # 板块数据
GET /api/v1/market/indices            # 指数数据
```

### WebSocket连接
```
WebSocket: /api/v1/websocket/market   # 统一市场数据推送
```

## 📋 字段命名标准化

### 统一使用驼峰命名
所有API返回数据统一使用驼峰命名格式：

```json
{
  "symbol": "000001",
  "name": "平安银行", 
  "currentPrice": 12.50,        // 不再使用 current_price
  "change": 0.15,
  "changePercent": 1.2,         // 不再使用 change_percent
  "volume": 1000000,
  "amount": 12500000,
  "timestamp": "2024-01-01T10:00:00"
}
```

## 🔒 安全信息处理

### Token管理
- ✅ 移除所有明文Token
- ✅ 使用环境变量占位符
- ✅ 更新所有.env文件示例

### 生产部署
生产环境Token请通过环境变量注入：
```bash
export TUSHARE_TOKEN="your-actual-token"
```

## 📖 文档更新状态

### 需要更新的文档
以下文档包含过时的API路径，需要更新：

1. `docs/market-api-spec.md` - 市场API规范
2. `docs/api-path-reference.md` - API路径参考
3. `docs/market-verification-checklist.md` - 市场验证清单
4. `docs/行情中心完整数据系统实现报告.md` - 行情中心报告
5. `docs/行情中心改进完成报告.md` - 改进报告
6. `docs/deployment/DEPLOYMENT_GUIDE.md` - 部署指南

### 过时命名清理
文档中以下命名已过时，请统一更新：
- ❌ `market-v2` → ✅ `market`
- ❌ `enhanced-market` → ✅ `market` 
- ❌ `market_unified` → ✅ `unified_market`
- ❌ `enhanced_historical` → ✅ `historical`

## 🚀 迁移建议

### 对于开发者
1. 更新API调用路径到新的统一路径
2. 修改字段访问从下划线改为驼峰命名
3. 移除对废弃API的依赖
4. 更新WebSocket连接配置

### 对于部署
1. 检查环境变量配置
2. 确保Token通过安全方式注入
3. 更新监控和日志配置
4. 验证新API路径可访问性

## ⚠️ 注意事项

1. **向后兼容**: 废弃的API文件已移至`deprecated/`目录，如需回滚可以恢复
2. **缓存清理**: 部署后建议清理前端和后端缓存
3. **文档同步**: 请及时更新项目文档和API文档
4. **测试验证**: 部署前请在测试环境验证所有API端点

## 📞 支持

如遇到API迁移相关问题，请参考：
- 统一市场API: `backend/app/api/v1/unified_market.py`
- 前端API客户端: `frontend/src/api/market_unified.ts`
- WebSocket服务: `frontend/src/services/market-websocket.service.ts`
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端快速测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>前端快速测试页面</h1>
        
        <div class="status info">
            <strong>测试项目：</strong>
            <ul>
                <li>前端服务连通性</li>
                <li>后端API连通性</li>
                <li>市场数据获取</li>
                <li>页面跳转功能</li>
            </ul>
        </div>

        <div id="status-container">
            <div class="status info">正在初始化测试...</div>
        </div>

        <div style="margin: 20px 0;">
            <button onclick="testFrontendConnection()">测试前端连接</button>
            <button onclick="testBackendConnection()">测试后端连接</button>
            <button onclick="testMarketAPI()">测试市场API</button>
            <button onclick="navigateToMarket()">跳转到行情页面</button>
            <button onclick="navigateToHome()">跳转到主页</button>
        </div>

        <div id="results">
            <h3>测试结果：</h3>
            <div id="test-output"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8000/api/v1';
        const FRONTEND_BASE_URL = 'http://localhost:5174';

        function log(message, type = 'info') {
            const output = document.getElementById('test-output');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `status ${type}`;
            logEntry.innerHTML = `<strong>[${timestamp}]</strong> ${message}`;
            output.appendChild(logEntry);
            console.log(`[${timestamp}] ${message}`);
        }

        function updateStatus(message, type = 'info') {
            const container = document.getElementById('status-container');
            container.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        async function testFrontendConnection() {
            log('开始测试前端连接...', 'info');
            try {
                const response = await fetch(FRONTEND_BASE_URL);
                if (response.ok) {
                    log('✅ 前端服务连接正常', 'success');
                    return true;
                } else {
                    log(`❌ 前端服务响应异常: ${response.status}`, 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ 前端服务连接失败: ${error.message}`, 'error');
                return false;
            }
        }

        async function testBackendConnection() {
            log('开始测试后端连接...', 'info');
            try {
                const response = await fetch(`${API_BASE_URL}/market/health`);
                const data = await response.json();
                if (data.success) {
                    log('✅ 后端API连接正常', 'success');
                    log(`后端状态: ${data.status}`, 'info');
                    return true;
                } else {
                    log(`❌ 后端API响应异常: ${JSON.stringify(data)}`, 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ 后端API连接失败: ${error.message}`, 'error');
                return false;
            }
        }

        async function testMarketAPI() {
            log('开始测试市场数据API...', 'info');
            try {
                const response = await fetch(`${API_BASE_URL}/market/overview`);
                const data = await response.json();
                if (data.success) {
                    log('✅ 市场数据API正常', 'success');
                    log(`市场状态: ${data.data.market_status}`, 'info');
                    log(`涨跌统计: 涨${data.data.stats.advancers}只, 跌${data.data.stats.decliners}只`, 'info');
                    
                    // 显示详细数据
                    const details = document.createElement('pre');
                    details.textContent = JSON.stringify(data.data, null, 2);
                    document.getElementById('test-output').appendChild(details);
                    
                    return true;
                } else {
                    log(`❌ 市场数据API响应异常: ${JSON.stringify(data)}`, 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ 市场数据API请求失败: ${error.message}`, 'error');
                return false;
            }
        }

        function navigateToMarket() {
            log('尝试跳转到行情页面...', 'info');
            try {
                window.open(`${FRONTEND_BASE_URL}/#/market`, '_blank');
                log('✅ 已打开行情页面新标签', 'success');
            } catch (error) {
                log(`❌ 跳转失败: ${error.message}`, 'error');
            }
        }

        function navigateToHome() {
            log('尝试跳转到主页...', 'info');
            try {
                window.open(`${FRONTEND_BASE_URL}`, '_blank');
                log('✅ 已打开主页新标签', 'success');
            } catch (error) {
                log(`❌ 跳转失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动运行基础测试
        window.addEventListener('load', async () => {
            updateStatus('正在运行自动测试...', 'info');
            
            log('=== 开始自动测试 ===', 'info');
            
            const frontendOK = await testFrontendConnection();
            const backendOK = await testBackendConnection();
            
            if (frontendOK && backendOK) {
                updateStatus('✅ 前后端服务均正常运行', 'success');
                await testMarketAPI();
            } else {
                updateStatus('❌ 检测到服务连接问题', 'error');
            }
            
            log('=== 自动测试完成 ===', 'info');
        });
    </script>
</body>
</html>